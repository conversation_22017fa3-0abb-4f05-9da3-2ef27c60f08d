# Google OAuth2 Setup Guide

This guide will help you set up Google OAuth2 authentication for your File Manager application.

## Prerequisites

- Google account
- Access to Google Cloud Console
- Your application running on a known URL (localhost for development)

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown and select "New Project"
3. Enter a project name (e.g., "File Manager App")
4. Click "Create"

## Step 2: Enable Google+ API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google+ API" 
3. Click on it and press "Enable"
4. Also enable "Google OAuth2 API" if available

## Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace account)
3. Fill in the required information:
   - **App name**: File Manager
   - **User support email**: Your email
   - **Developer contact information**: Your email
4. Add scopes:
   - `openid`
   - `profile` 
   - `email`
5. Add test users (your email and any other emails you want to test with)
6. Save and continue

## Step 4: Create OAuth2 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Web application"
4. Configure the settings:
   - **Name**: File Manager OAuth Client
   - **Authorized JavaScript origins**: 
     - `http://localhost:8080` (for development)
     - `https://yourdomain.com` (for production)
   - **Authorized redirect URIs**:
     - `http://localhost:8080/login/oauth2/code/google` (for development)
     - `https://yourdomain.com/login/oauth2/code/google` (for production)
5. Click "Create"
6. Copy the **Client ID** and **Client Secret**

## Step 5: Configure Your Application

### Option A: Using application.properties

Update `src/main/resources/application.properties`:

```properties
# Replace with your actual values
spring.security.oauth2.client.registration.google.client-id=YOUR_GOOGLE_CLIENT_ID
spring.security.oauth2.client.registration.google.client-secret=YOUR_GOOGLE_CLIENT_SECRET
```

### Option B: Using Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your credentials:
   ```bash
   GOOGLE_CLIENT_ID=your_actual_client_id_here
   GOOGLE_CLIENT_SECRET=your_actual_client_secret_here
   ```

3. Update `application.properties` to use environment variables:
   ```properties
   spring.security.oauth2.client.registration.google.client-id=${GOOGLE_CLIENT_ID}
   spring.security.oauth2.client.registration.google.client-secret=${GOOGLE_CLIENT_SECRET}
   ```

## Step 6: Test the Setup

1. Start your application:
   ```bash
   ./gradlew bootRun
   ```

2. Open your browser and go to `http://localhost:8080`

3. You should be redirected to the login page

4. Click "Continue with Google"

5. Complete the Google OAuth flow

6. You should be redirected back to the file manager with your user info displayed

## Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch" error**:
   - Check that your redirect URI in Google Console exactly matches: `http://localhost:8080/login/oauth2/code/google`
   - Make sure there are no trailing slashes or extra characters

2. **"invalid_client" error**:
   - Verify your Client ID and Client Secret are correct
   - Check for any extra spaces or characters when copying

3. **"access_denied" error**:
   - Make sure your email is added as a test user in the OAuth consent screen
   - Check that the required scopes are configured

4. **Application not redirecting after login**:
   - Check the application logs for any errors
   - Verify the SecurityConfig is properly configured

### Development vs Production

- **Development**: Use `http://localhost:8080`
- **Production**: Use your actual domain with HTTPS (e.g., `https://yourdomain.com`)

### Security Notes

- Never commit your Client Secret to version control
- Use environment variables or secure configuration management in production
- Consider using Google Cloud Secret Manager for production deployments
- Regularly rotate your OAuth credentials

## Next Steps

Once authentication is working:

1. Consider adding user-specific file storage (each user sees only their files)
2. Implement role-based access control
3. Add user profile management
4. Set up proper session management
5. Configure HTTPS for production

## Support

If you encounter issues:

1. Check the application logs for detailed error messages
2. Verify your Google Cloud Console configuration
3. Test with a simple OAuth2 flow first
4. Consult the [Spring Security OAuth2 documentation](https://docs.spring.io/spring-security/reference/servlet/oauth2/login/index.html)
